<?php
/**
 * Database Connection Test
 * This script tests the database connection and checks for required tables
 */

// Include database configuration
require_once 'config/database.php';

echo "<h2>Database Connection Test</h2>";

try {
    // Test basic connection
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check if database exists
    $result = $db->fetch("SELECT DATABASE() as current_db");
    echo "<p>Current database: <strong>" . ($result['current_db'] ?? 'None') . "</strong></p>";
    
    // Check for required tables
    $requiredTables = [
        'users', 'user_roles', 'blood_types', 'donor_profiles', 
        'recipient_profiles', 'blood_requests', 'donations'
    ];
    
    echo "<h3>Table Status:</h3>";
    echo "<ul>";
    
    foreach ($requiredTables as $table) {
        try {
            $result = $db->fetch("SHOW TABLES LIKE '$table'");
            if ($result) {
                echo "<li style='color: green;'>✅ Table '$table' exists</li>";
                
                // Check table structure for users table
                if ($table === 'users') {
                    $columns = $db->fetchAll("SHOW COLUMNS FROM users");
                    $columnNames = array_column($columns, 'Field');
                    
                    $requiredColumns = ['id', 'username', 'password', 'user_type', 'is_unified_user', 'registration_source'];
                    $missingColumns = array_diff($requiredColumns, $columnNames);
                    
                    if (empty($missingColumns)) {
                        echo "<li style='color: green;'>✅ Users table has all required columns</li>";
                    } else {
                        echo "<li style='color: orange;'>⚠️ Users table missing columns: " . implode(', ', $missingColumns) . "</li>";
                    }
                }
            } else {
                echo "<li style='color: red;'>❌ Table '$table' missing</li>";
            }
        } catch (Exception $e) {
            echo "<li style='color: red;'>❌ Error checking table '$table': " . $e->getMessage() . "</li>";
        }
    }
    echo "</ul>";
    
    // Test a simple query
    try {
        $bloodTypes = $db->fetchAll("SELECT * FROM blood_types LIMIT 1");
        echo "<p style='color: green;'>✅ Sample query successful</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Sample query failed: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<h3>Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check if database 'blood_donation_system' exists</li>";
    echo "<li>Verify database credentials in config/database.php</li>";
    echo "<li>Run the database setup scripts</li>";
    echo "</ul>";
}
?>
